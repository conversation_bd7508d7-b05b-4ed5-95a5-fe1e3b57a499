import dash
import dash_bootstrap_components as dcb
from dash import dcc, html, Input, Output
import pandas as pd
import plotly.express as px
from collections import deque
import time
import random
import threading 
import serial 
from xbee import XBee 
import csv
import signal
import sys
import math 

#Serial Parameters
serial_port = "/dev/tty.usbserial-AH065Q59" #may change to my port later
baud_rate = 56700
serial_conn = None
xbee = None
mock = True
counter = 0

#Defining mapping from sensor names to graph IDs, and units
sensor_info = {
    "finger_force": "graph-finger-force",
    "thumb_force" : "graph-thumb-force", 
}

#Data storage
max_points = 1000
frame_data = deque(maxlen = max_points)

streams = {
    "finger_force" : deque(maxlen = max_points),
    "thumb_force" : deque(maxlen = max_points)
}

saved_data = []
running = True
data_thread = None
app = dash.Dash(__name__)

def signal_handler(sig, frame):
    print("\nReceived keyboard interrupt, stopping gracefully...")
    stop() #A function created by the programmer see below...
    sys.exit(0) #exit w code 0?

def connect_serial(): #connecting program to serial port
    global xbee, serial_conn

    try:
        serial_conn = serial.Serial(
            baudrate=baudrate,
            bytesize=8,
            port=serial_port,
            timeout=5
        )
        xbee = Xbee(serial_conn)
        print("Serial connection established.")
        return True #True if connection successful

    except Exception as e:
        print("Error connecting to serial port:", e)
        serial_conn = None
        xbee = None 
        return False #False if connection failed

def serial_read(): #Reads data from xbee and converts it to hexidecimal data
    global mock, xbee, data_thread

    try: #If xbee not initialized but program is running, start a thread using mock data. End thread if main program ends.
        if xbee is None:
            print("XBee not initialized. Stopping serial reader.")
            mock = True 

            if running:
                data_thread = threading.Thread(target=reading_mock_data, daemon=True)
                data_thread.start()
            return 
        
        #extracting data and turning it into a readable format
        raw_data = xbee.wait_read_frame()
        data = raw_data["rf_data"]
        hex_data = data.hex()
        process_data(hex_data)

    except:
        pass

def process_data(data):
    if not running:
        return

    for value in data: #remove potentially
        count += 0.5 #a full data set is 2bytes so each byte accounts for 0.5
        frame_data.append(count)

    saved_data.append(data) #saving data to general storage
    
    bytes_data = [data[i:i+2] for i in range(0, len(data), 2)] #need to join these as two bytes -> the singular number
    thumb_bytes = "".join(bytes_data[11:13])
    finger_bytes = "".join(bytes_data[13:15])
    thumb_voltage = (int(thumb_bytes, 16)/1023)*3.3 
    finger_voltage = (int(finger_bytes, 16)/1023)*3.3
    thumb_force = math.exp((thumb_voltage - 1.47) / 0.23)
    finger_force = math.exp((finger_voltage + 2.8) / 1.02) 
    
    #save final values to stream specific data
    streams["thumb_force"].append(thumb_force)
    streams["finger_force"].append(finger_force)

def serial_reader_thread():
    while running:
        try:
            if mock or xbee is None:
                print("Serial connection not available. Exiting serial reader thread.")
                return
            serial_read()
        except Exception as e:
            if running:
                print(f"Error in serial reader thread: {e}")
                time.sleep(1)

def mock_serial_read():
    if not running:
        return

    thumb_force = random.uniform(0,80)
    finger_force = random.uniform(0,80)
    streams["thumb_force"].append(thumb_force)
    streams["finger_force"].append(finger_force)
    time.sleep(0.1) #gives a realistic amount of time in between data collection

def mock_reader_thread():
    while running:
        try:
            if not mock:
                print("Switched to real data mode. Exiting mock reader thread")
                return
            mock_serial_read()

        except Exception as e:
            print(f"Error in mock data thread: {e}")
            time.sleep(1)

def save_to_file():
    try:
        timestamp = time.strftime("%Y%m%d-%H%M%S")
        filename = f"Data_Log-{timestamp}.csv"
        with open(filename,"w",newline='') as file:
            writer = csv.writer(file)
            header = list(streams.keys())
            writer.writerow(header)
            for data in saved_data:
                writer.writerow(data)
            return filename

    except Exception as e:
        print(f"Error saving data: {e}")
        return None

def stop():
    global running

    if not running:
        print("Program already stopped.")
        return

    time.sleep(0.5)

    if serial_conn:
        try:
            serial_conn.close()
            print(f"Serial connection successfully closed.")
        except Exception as e:
            print(f"Error closing serial connection: {e}")

    filename = save_to_file()
    if filename:
        print(f"Program successfully stopped. Data saved to file {filename}.")
        return filename
    else:
        print(f"Program successfully stopped however, data was unable to be saved.")


