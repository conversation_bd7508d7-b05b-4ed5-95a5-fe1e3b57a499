"""
Example: Using KTK Player for Sledge Hockey Motion Analysis
===========================================================

This script demonstrates how to use the improved KTK Player functionality
in the Sledge Hockey Motion Analysis system.

The improvements follow the official Kinetics Toolkit documentation:
https://kineticstoolkit.uqam.ca/doc/player_basics.html

Features demonstrated:
- Basic KTK Player usage
- Proper orientation for sledge hockey data
- Skeleton connections
- Interactive controls
- Comprehensive visualization
"""

import os
from pathlib import Path
from optitrack_kinetics_analyzer import SledgeHockeyAnalyzer

def main():
    """
    Demonstrate KTK Player usage with sledge hockey data
    """
    print("🏒 KTK Player Demo for Sledge Hockey Motion Analysis")
    print("=" * 60)
    
    # Initialize the analyzer
    analyzer = SledgeHockeyAnalyzer()
    
    # Look for Optitrack files in the data directory
    data_dir = Path("data/optitrack")
    if not data_dir.exists():
        print(f"❌ Data directory not found: {data_dir}")
        print("Please ensure you have Optitrack CSV files in the data/optitrack directory")
        return
    
    # Find CSV files
    csv_files = list(data_dir.glob("*.csv"))
    if not csv_files:
        print(f"❌ No CSV files found in {data_dir}")
        print("Please add some Optitrack CSV files to analyze")
        return
    
    print(f"📁 Found {len(csv_files)} CSV files:")
    for i, file in enumerate(csv_files, 1):
        print(f"   {i}. {file.name}")
    
    # Use the first file for demonstration
    selected_file = csv_files[0]
    print(f"\n📊 Loading data from: {selected_file.name}")
    
    try:
        # Load the data
        ts_markers = analyzer.load_optitrack_csv(str(selected_file))
        print(f"✅ Successfully loaded motion data")
        
        # Create output directory
        output_dir = analyzer.create_output_directory("ktk_player_demo")
        
        print("\n" + "=" * 60)
        print("🎬 KTK Player Visualization Options")
        print("=" * 60)
        
        # Option 1: Simple KTK Player (following documentation example)
        print("\n1️⃣  Simple KTK Player (Basic Documentation Example)")
        print("-" * 50)
        try:
            player = analyzer.create_simple_ktk_player()
            print("✅ Simple KTK Player created and started")
            print("   This follows the basic documentation pattern")
            print("   The player window should now be open")
        except Exception as e:
            print(f"❌ Could not create simple player: {e}")
        
        # Option 2: Enhanced visualization with skeleton
        print("\n2️⃣  Enhanced Visualization with Skeleton")
        print("-" * 50)
        try:
            viz_path = analyzer.visualize_with_ktk(show_skeleton=True, show_coordinate_systems=True)
            print(f"✅ Enhanced visualization created")
            print(f"   Reference guide: {viz_path}")
        except Exception as e:
            print(f"❌ Could not create enhanced visualization: {e}")
        
        # Option 3: Comprehensive visualization
        print("\n3️⃣  Comprehensive Visualization (Full Dataset)")
        print("-" * 50)
        try:
            comp_path = analyzer.create_ktk_comprehensive_visualization()
            print(f"✅ Comprehensive visualization created")
            print(f"   Reference guide: {comp_path}")
        except Exception as e:
            print(f"❌ Could not create comprehensive visualization: {e}")
        
        print("\n" + "=" * 60)
        print("🎮 How to Use the KTK Player")
        print("=" * 60)
        print("The KTK Player should now be open in a separate window.")
        print("If you don't see it, check your taskbar or try running again.")
        print()
        print("Key Controls:")
        print("  SPACE     - Play/Pause")
        print("  ←/→       - Step frame by frame")
        print("  +/-       - Speed up/slow down")
        print("  h         - Show help overlay")
        print("  1-6       - Preset camera views")
        print("  0         - Reset to initial view")
        print("  t         - Toggle tracking mode")
        print()
        print("Mouse Controls:")
        print("  Left-drag    - Rotate 3D view")
        print("  Right-drag   - Zoom in/out")
        print("  Middle-drag  - Pan view")
        print("  Left-click   - Select marker")
        print()
        print("🔬 Analysis Tips:")
        print("  - Use tracking mode (t) to follow player movement")
        print("  - Slow down playback (+/-) for detailed analysis")
        print("  - Switch views (1-6) to analyze different planes")
        print("  - Step through frames for precise measurements")
        
        print("\n" + "=" * 60)
        print("✅ Demo Complete!")
        print("=" * 60)
        print("The KTK Player windows should remain open for interactive use.")
        print("Close this script when you're done exploring the visualizations.")
        
    except Exception as e:
        print(f"❌ Error during demo: {e}")
        print("Please check your data files and try again.")

if __name__ == "__main__":
    main()
