import dash 
from dash import dcc, html
from dash.dependencies import Input, Output, State
import dash_bootstrap_components as dbc
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import data_processing as dp

def setup_layout(app):
    # morph_theme = "https://cdn.jsdelivr.net/npm/bootswatch@5.3.2/dist/morph/bootstrap.min.css" #adding morph theme
    # dbc_css = "https://cdn.jsdelivr.net/gh/AnnMarieW/dash-bootstrap-templates/dbc.min.css" #styling and spacing helper
    # google_font = "https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap" #adding fonts that are unavaliable in bootstrap
    # app = dash.Dash(__name__, external_stylesheets=[dbc.themes.BOOTSTRAP,dbc_css,morph_theme,google_font]) #this line creates the web application
    app.layout = dbc.Container([
        #Title
        dbc.Row([ #dbc is based off of a 12 column system. Within each row information is arranged in column.
            dbc.Col(html.H1
            ("Force Cuff Data Visualization", className= "text-center fs-1 fw-bold",
            style= {"fontFamily":"Jost"}
            ))
        ]),

        #Mock and serial 
        dbc.Row([
            dbc.Col(
                html.H2("Data Source Selection:", className="fs-3", style={"fontFamily": "Jost"}),
                width="auto"
                ),
            dbc.Col(
                dbc.Button("Mock", id="mock-btn", className="me-2"),
                width="auto"
                ),
            dbc.Col(
                dbc.Button("Serial", id="serial-btn", className="me-2"),
                width="auto"
                )
        ], justify="center"),


        #Graph display cards
        dbc.Row([
            #Left Card
            dbc.Col(
                dbc.Card([
                    dbc.CardBody([
                        html.H4(
                            "Force per Frame",
                            className="card-title text-center",
                            style= {"fontFamily":"Jost"},
                        ),
                        dcc.Graph(id="force-line-graph"),
                    ])
                ],
                className="w-100 h-100",
                color="white"
                ),
                width = 5,
                className="d-flex align-items-stretch mx-3 mt-3 mb-0",
            ),

            #Right card
            dbc.Col(
                dbc.Card([
                    dbc.CardBody([
                        html.H4(
                            "Visual Representation",
                            className="card-title text-center",
                            style= {"fontFamily":"Jost"},
                        )
                    ])
                ],
                className="w-100 h-100",
                color="white",
                ),
                width = 5,
                className="d-flex align-items-stretch mx-3 mt-3 mb-0",
                style={"height": "600px"},
            ),
        ],
        justify="center",),

        #Stop and save button
        dbc.Row([
            dbc.Col(
                dbc.Button("Stop and Save", id="stop-btn", className="me-2"),
                width="auto"
                )
        ], justify="center", className="mt-0 pt-0 mb-5"),

        dcc.Interval(
                id='interval_component',
                interval=100,  # in milliseconds
                n_intervals=0
            ),
    ],
        fluid=True,
        className="dbc"
    )
    
def setup_callbacks(app):
    #Updating the graph 
    for sensor_name, graph_id in dp.sensor_info.items():
        @app.callback(
            Output(graph_id, 'figure'),
            Input('interval_component', 'n_intervals')
        )
        def update_graph_for_sensor(n_intervals):
            print(f"Callback triggered for {graph_id}, n_intervals={n_intervals}")
            y_data1 = dp.streams["thumb_force"]
            y_data2 = dp.streams["finger_force"]
            x_data = list(range(len(y_data1)))

            fig = go.Figure()

            fig.add_trace(go.Scatter(
                x = x_data,
                y = dp.streams["thumb_force"],
                mode = 'lines',
                name = 'Thumb Force',
                line = dict(color = '#7EC636')
            ))

            fig.add_trace(go.Scatter(
                x = x_data,
                y = dp.streams["finger_force"],
                mode = 'lines',
                name = 'Finger Force',
                line = dict(color = '#E4B7FF')
            ))

            fig.update_layout(
                xaxis_title = "Frame",
                yaxis_title = "Force (N)",
            )

            return fig
        return update_graph_for_sensor



##BUTTON CALLBACK SUGGESTION

# Update store data on button click
# @app.callback(
#     Output("data-source-store", "data"),
#     Input("mock-btn", "n_clicks"),
#     Input("serial-btn", "n_clicks"),
#     prevent_initial_call=True
# )
# def update_data_source(mock_clicks, serial_clicks):
#     ctx = dash.callback_context

#     if not ctx.triggered:
#         raise dash.exceptions.PreventUpdate

#     button_id = ctx.triggered[0]["prop_id"].split(".")[0]

#     if button_id == "mock-btn":
#         return {"mock": True, "serial": False}
#     elif button_id == "serial-btn":
#         return {"mock": False, "serial": True}

#     return dash.no_update


# Update button appearance based on selection
# @app.callback(
#     Output("mock-btn", "color"),
#     Output("mock-btn", "active"),
#     Output("serial-btn", "color"),
#     Output("serial-btn", "active"),
#     Input("data-source-store", "data")
# )
# def update_button_styles(data):
#     mock_color = "primary" if data["mock"] else "secondary"
#     serial_color = "primary" if data["serial"] else "secondary"

#     return mock_color, data["mock"], serial_color, data["serial"]


# # Optional debug display
# @app.callback(
#     Output("output-div", "children"),
#     Input("data-source-store", "data")
# )
# def display_state(data):
#     return f"Mock: {data['mock']}, Serial: {data['serial']}"


# if __name__ == "__main__":
#     app.run(debug=True)