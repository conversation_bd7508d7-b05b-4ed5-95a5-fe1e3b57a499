#Main function for Force Cuff Dashboard which starts the visualization
import signal
import data_processing as dp
import dashboard_layout as dl
import threading
import dash
import dash_bootstrap_components as dbc

def main():
    #Initializing variables
    global mock, data_thread, running
    mock = True
    running = True

    #Setting up signal handler (CTRL + C -> shutdown)
    signal.signal(signal.SIGINT, dp.signal_handler) 

    #Testing serial port and starting thread
    if not mock:
        # Try to connect to serial, if it fails, switch to mock mode
        if not dp.connect_serial():
            mock = True
            print("Failed to connect to serial port, switching to mock data mode.")
            data_thread = threading.Thread(target=dp.mock_reader_thread, daemon=True) #serial port failed start mock thread
        else:
            data_thread = threading.Thread(target=dp.serial_reader_thread, daemon=True) #serial port succeeded start serial thread
    else:
        data_thread = threading.Thread(target=dp.mock_reader_thread, daemon=True) #mock is true so start mock thread
    
    data_thread.start()

    # Setup Dash application
    morph_theme = "https://cdn.jsdelivr.net/npm/bootswatch@5.3.2/dist/morph/bootstrap.min.css" #adding morph theme
    dbc_css = "https://cdn.jsdelivr.net/gh/AnnMarieW/dash-bootstrap-templates/dbc.min.css" #styling and spacing helper
    google_font = "https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap" #adding fonts that are unavaliable in bootstrap
    app = dash.Dash(__name__, external_stylesheets=[dbc.themes.BOOTSTRAP,dbc_css,morph_theme,google_font]) #this line creates the web application
    
    dl.setup_layout(app)
    dl.setup_callbacks(app)

    # Start the server
    print(f"Starting visualization server {'with mock data' if mock else ''}")
    print("Press Ctrl+C to stop the server or use the 'Stop and Save Data' button in the UI")
    try:
        app.run(debug=False)
    except KeyboardInterrupt:
        print("\nKeyboard interrupt received. Stopping...")
        dp.stop()
    except Exception as e:
        print(f"Error in Dash server: {e}")
        dp.stop()
    finally:
        # Ensure resources are cleaned up
        if running:
            dp.stop()

if __name__ == "__main__":
    main()